<div align="center">
  <img alt="Prisma Logo" src="public/prisma_logo_linear.png" width="580" />
</div>
<br />

# PRISMA - Programma Interno per la Scansione dei Manoscritti

**PRISMA** is a specialized desktop application developed for the Vatican Apostolic Library (BAV) to manage and control the digitization process of manuscripts and historical documents. Built with Electron and Svelte, it provides a comprehensive interface for scanner control, image processing, and manuscript cataloging.

<br />
<div align="center">

[![Built with Electron](https://img.shields.io/badge/Built%20with-Electron-47848f.svg)](https://electronjs.org/)
[![Built with Svelte](https://img.shields.io/badge/Built%20with-Svelte-ff3e00.svg)](https://svelte.dev/)
[![Version](https://img.shields.io/badge/Version-1.3.0-blue.svg)](package.json)

</div>

## ✒️ Overview

PRISMA is a professional manuscript digitization application designed specifically for the Vatican Apostolic Library. It provides comprehensive tools for:

- **Scanner Control**: Direct integration with Metis scanning devices for high-quality image capture
- **Image Processing**: Advanced image processing capabilities using Sharp library for TIFF metadata, cropping, and color calibration
- **Manuscript Management**: Complete workflow management for digitization projects with job tracking and nomenclature systems
- **Quality Control**: Built-in validation tools and preview systems for ensuring digitization quality
- **Database Integration**: MySQL integration for storing scan metadata, job information, and user activity logs
- **Authentication**: LDAP integration for secure user authentication within the BAV network

## 🔧 Key Technologies & Dependencies

### Core Framework
- **`electron`** (v30.0.0) - Cross-platform desktop application framework
- **`svelte`** (v3.42.1) - Reactive UI framework for the frontend
- **`svelte-spa-router`** - Client-side routing for the application

### Scanner Integration
- **Metis Scanner Integration** - Custom TCP socket communication for scanner control
- **`sharp`** (v0.31.3) - High-performance image processing for TIFF manipulation and metadata

### Database & Authentication
- **`mysql2`** (v3.1.2) - MySQL database connectivity for job and scan management
- **`ldap-authentication`** (v2.2.9) - LDAP integration for BAV network authentication

### UI & User Experience
- **`svelte-material-ui`** (v6.0.0-beta.15) - Material Design components
- **`svelte-material-icons`** (v2.0.0) - Material Design icons
- **`@zerodevx/svelte-toast`** (v0.9.5) - Toast notifications
- **`d3-selection`** (v3.0.0) - DOM manipulation for image viewers

### Image Viewing & Processing
- **OpenSeaDragon** - Deep zoom image viewer for high-resolution manuscript viewing
- **Custom DZI Generation** - Deep Zoom Images for smooth pan/zoom experience

### Development & Build Tools
- **`electron-builder`** (v24.9.1) - Application packaging and distribution
- **`electron-updater`** (v6.1.4) - Automatic application updates
- **`rollup`** (v2.56.2) - Module bundler for the frontend code
<br />

## 🚀 Getting Started

### Prerequisites

- **Node.js** (v16 or higher)
- **Yarn** package manager (recommended) or npm
- **Access to BAV Network** for LDAP authentication and database connectivity
- **Metis Scanner** (for production use)

### Installation & Development

```bash
# Clone the repository
$ git clone http://git.bav.local:3000/BAV/prisma.git

# Navigate to project directory
$ cd prisma

# Install dependencies
$ yarn install

# Start development environment
$ yarn electron-dev

# Build for production
$ yarn electron-pack
```

### Available Scripts

- `yarn dev` - Start Svelte development server with hot reload
- `yarn build` - Build the Svelte application for production
- `yarn electron-dev` - Start the application in development mode
- `yarn electron-pack` - Package the application for distribution
- `yarn start` - Serve the built application locally

## 🏗️ Architecture

PRISMA follows a modern Electron architecture with secure IPC communication between processes:

### Main Process (`src/main-process/`)
The main process handles:
- **Application lifecycle** and window management
- **IPC handlers** for database, filesystem, authentication, and scanner operations
- **Security** with sandboxed renderer process and controlled API exposure
- **Auto-updates** from the BAV internal update server
- **Protocol handlers** for local file access (`local://` and `isilon://`)

### Renderer Process (`src/renderer-process/`)
The frontend application built with Svelte includes:
- **Pages**: Login, Job Manager, Acquisition interface, Preview Visualizer
- **Components**: Modular UI components for scanner control, image viewing, and data management
- **Store**: Centralized state management using Svelte stores
- **Routing**: SPA routing with `svelte-spa-router`

### Key Features

#### Scanner Integration
- Direct TCP communication with Metis scanning devices
- Real-time scan progress monitoring
- Automatic image processing and metadata embedding
- Color calibration and quality control tools

#### Image Management
- **OpenSeaDragon** integration for high-resolution image viewing
- **DZI (Deep Zoom Images)** generation for smooth pan/zoom
- **Sharp** library for TIFF processing and metadata manipulation
- Automatic cropping and image optimization

#### Database Integration
- MySQL connectivity for job tracking and metadata storage
- User activity logging with scanner and host information
- Job recovery and validation workflows

#### User Interface
- **Material Design** components with Svelte Material UI
- **Responsive layout** optimized for scanning workflows
- **Toast notifications** for user feedback
- **Keyboard shortcuts** for efficient operation

### IPC Communication Architecture

PRISMA uses Electron's secure IPC (Inter-Process Communication) system to enable communication between the renderer process (UI) and the main process (backend services). This architecture ensures security by keeping the renderer process sandboxed while providing controlled access to system resources.

#### IPC Flow Overview

```
┌─────────────────┐    IPC Messages    ┌──────────────────┐
│  Renderer       │ ──────────────────► │  Main Process    │
│  (Svelte UI)    │                     │  (Node.js)       │
│                 │ ◄────────────────── │                  │
└─────────────────┘    IPC Responses    └──────────────────┘
        │                                        │
        │                                        ▼
        ▼                               ┌──────────────────┐
┌─────────────────┐                     │  IPC Handlers    │
│  Preload Script │                     │  ┌─────────────┐ │
│  (Security      │                     │  │ database    │ │
│   Bridge)       │                     │  │ filesystem  │ │
└─────────────────┘                     │  │ metis       │ │
                                        │  │ sharp       │ │
                                        │  │ auth        │ │
                                        │  └─────────────┘ │
                                        └──────────────────┘
```

#### Preload Script Security Bridge

The `client-preload.js` file acts as a secure bridge between the renderer and main processes:

```js
// src/main-process/client-preload.js
const { ipcRenderer, contextBridge } = require('electron/renderer');

contextBridge.exposeInMainWorld(
  'electron',
  {
    // Expose IPC handlers to renderer process
    application: (command, params) => ipcRenderer.invoke('application', command, params),
    variables: (command, params) => ipcRenderer.invoke('variables', command, params),
    auth: (command, params) => ipcRenderer.invoke('auth', command, params),
    metis: (command, params) => ipcRenderer.invoke('metis', command, params),
    database: (command, params) => ipcRenderer.invoke('database', command, params),
    filesystem: (command, params) => ipcRenderer.invoke('filesystem', command, params),
    sharp: (command, params) => ipcRenderer.invoke('sharp', command, params),
    worker: (command, params) => ipcRenderer.invoke('worker', command, params),
    ssh: (command, params) => ipcRenderer.invoke('ssh', command, params)
  }
);
```

#### IPC Handler Registration

In the main process (`src/main-process/index.js`), IPC handlers are registered for each service:

```js
const createHandlers = () => {
    // Database operations
    ipcMain.handle('database', async (event, ...args) => {
        const command = args[0];  // e.g., 'db-get-jobs'
        const params = args[1];   // parameters object
        const fn = database[command];
        return await fn.call(this, params);
    });

    // Scanner (Metis) operations
    ipcMain.handle('metis', async (event, ...args) => {
        const command = args[0];  // e.g., 'msd-scan'
        const params = args[1];
        const fn = metis[command];
        return await fn.call(this, params);
    });

    // Image processing operations
    ipcMain.handle('sharp', async (event, ...args) => {
        const command = args[0];  // e.g., 'build-dzi'
        const params = args[1];
        const fn = sharp[command];
        return await fn.call(this, params);
    });

    // ... other handlers
};
```

#### Practical IPC Usage Examples

**Database Operations:**
```js
// Get all jobs for current user
const jobs = await window.electron.database('db-get-jobs', { userId: currentUser.id });

// Update scan metadata
const result = await window.electron.database('db-update-scan', {
    scanId: 123,
    metadata: { pageNumber: 5, scanType: 'recto' }
});

// Log user activity
await window.electron.database('db-log-activity', {
    action: 'scan_completed',
    details: { scanId: 123, duration: 45000 }
});
```

**Scanner (Metis) Operations:**
```js
// Connect to scanner
const connection = await window.electron.metis('msd-connect', {
    host: '*************',
    port: 6000
});

// Perform scan with parameters
const scanResult = await window.electron.metis('msd-scan', {
    resolution: 600,
    colorMode: 'color',
    cropArea: { x: 0, y: 0, width: 2000, height: 3000 }
});

// Set TIFF metadata
await window.electron.metis('msd-set-tiff-metadata', {
    description: 'Vatican Manuscript Page 5',
    author: 'BAV Digitization Team',
    copyright: 'Vatican Apostolic Library'
});
```

**Image Processing:**
```js
// Generate DZI (Deep Zoom Images) for viewer
const dziPath = await window.electron.sharp('build-dzi', {
    inputPath: '/path/to/scan.tiff',
    outputDir: '/path/to/dzi/',
    tileSize: 256
});

// Process and crop image
const processedImage = await window.electron.sharp('process-image', {
    inputPath: '/path/to/original.tiff',
    cropArea: { x: 100, y: 100, width: 1800, height: 2700 },
    outputPath: '/path/to/cropped.tiff'
});

// Extract image metadata
const metadata = await window.electron.sharp('get-metadata', {
    imagePath: '/path/to/scan.tiff'
});
```

**File System Operations:**
```js
// Create job directory structure
await window.electron.filesystem('create-job-structure', {
    jobId: 'JOB_2024_001',
    basePath: '/scans/manuscripts/'
});

// Move and rename scan files
await window.electron.filesystem('organize-scan-files', {
    scanId: 123,
    sourceDir: '/temp/scans/',
    targetDir: '/archive/JOB_2024_001/'
});
```

**Authentication:**
```js
// LDAP authentication
const authResult = await window.electron.auth('ldap-authenticate', {
    username: '<EMAIL>',
    password: 'userPassword'
});

// Get current user profile
const userProfile = await window.electron.auth('get-user-profile', {
    username: '<EMAIL>'
});
```

#### Error Handling in IPC

All IPC calls should be wrapped in try-catch blocks for proper error handling:

```js
try {
    const result = await window.electron.database('db-get-jobs', params);
    // Handle success
} catch (error) {
    console.error('Database operation failed:', error);
    // Show user-friendly error message
    toast.push('Failed to load jobs. Please try again.', {
        theme: { '--toastBackground': '#f56565', '--toastColor': 'white' }
    });
}
```

#### IPC Handler Structure

Each IPC handler module exports functions that correspond to commands:

```js
// src/main-process/ipc-handlers/database.js
const mysql = require('mysql2/promise');

const dbGetJobs = async (params) => {
    const connection = await mysql.createConnection(dbOptions);
    const [rows] = await connection.execute(
        'SELECT * FROM jobs WHERE user_id = ?',
        [params.userId]
    );
    await connection.end();
    return rows;
};

const dbUpdateScan = async (params) => {
    // Implementation for updating scan data
};

module.exports = {
    'db-get-jobs': dbGetJobs,
    'db-update-scan': dbUpdateScan,
    // ... other database operations
};
```

### Project Structure
```
src/
├── main-process/           # Electron main process
│   ├── ipc-handlers/      # IPC command handlers
│   │   ├── application.js # App lifecycle operations
│   │   ├── auth.js        # LDAP authentication
│   │   ├── database.js    # MySQL operations
│   │   ├── filesystem.js  # File system operations
│   │   ├── metis.js       # Scanner communication
│   │   ├── sharp.js       # Image processing
│   │   ├── ssh.js         # SSH operations
│   │   ├── variables.js   # Configuration variables
│   │   └── worker.js      # Background workers
│   ├── utilities/         # Utility functions
│   ├── workers/           # Background workers
│   ├── client-preload.js  # Security bridge
│   ├── config.js          # Application configuration
│   └── index.js           # Main process entry point
└── renderer-process/       # Svelte frontend
    ├── components/        # Reusable UI components
    │   ├── color-calibration/
    │   ├── job-manager/
    │   ├── metis-scan-director/
    │   ├── open-sea-dragon/
    │   ├── preview-visualizer/
    │   ├── settings/
    │   └── toolbar/
    ├── pages/            # Application pages/routes
    │   ├── Acquisizione.svelte    # Main scanning interface
    │   ├── App.svelte             # Root component
    │   ├── JobManager.svelte      # Job management
    │   ├── Login.svelte           # Authentication
    │   └── PreviewVisualizer.svelte
    ├── utilities/        # Frontend utilities
    ├── routes.js         # SPA routing configuration
    ├── store.js          # Svelte stores (state management)
    └── main.js           # Renderer entry point
```

## 🔐 Configuration

### Database Configuration
Update `src/main-process/config.js` with your MySQL database settings:

```js
exports.dbOptions = {
  host: "your-database-host",
  port: 3306,
  database: "your-database-name",
  user: "your-username",
  password: "your-password"
}
```

### LDAP Authentication
Configure LDAP settings for BAV network authentication:

```js
exports.ldapOptions = {
  url: 'ldap://your-ldap-server',
  bindDN: 'your-bind-dn',
  bindCredentials: 'your-credentials',
  searchBase: 'your-search-base',
  searchFilter: '(|(sAMAccountName={{username}})(mail={{username}}))'
}
```

## 📦 Building & Distribution

### Development Build
```bash
yarn build
```

### Production Package
```bash
yarn electron-pack
```

The application will be packaged using `electron-builder` with the following features:
- **Auto-updater** integration with BAV internal server
- **NSIS installer** for Windows deployment
- **Code signing** (configure in `package.json` build section)

## 🔄 Version History

- **v1.3.0** - Current stable version
- **v7.0.0-alpha** - Major overhaul with validator functionality
- **v1.0.0-beta** - DZI image generation and improved viewers
- **v1.0.0** - Initial production release

## 🤝 Contributing

This is an internal BAV project. For development:

1. Follow the existing code style and patterns
2. Test thoroughly with actual scanning hardware when possible
3. Update documentation for any new features
4. Ensure compatibility with the BAV network infrastructure

## 📄 License

Internal use only - Vatican Apostolic Library (BAV)

---

**PRISMA** - Enhancing the preservation and accessibility of historical manuscripts through modern digitization technology.