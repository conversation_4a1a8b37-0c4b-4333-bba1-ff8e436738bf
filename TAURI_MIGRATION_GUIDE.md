# Prisma to Tauri Migration Guide

## Project Overview

Prisma is a sophisticated manuscript scanning system currently built with Electron + Svelte. This guide outlines a comprehensive migration strategy to Tau<PERSON> for improved performance, security, and maintainability.

## Current Architecture Analysis

### Technology Stack
- **Frontend**: Svelte 3 + Svelte Material UI + OpenSeadragon
- **Backend**: Electron main process with Node.js
- **Database**: MySQL with mysql2 driver
- **Image Processing**: Sharp library
- **Scanner Communication**: TCP sockets to Metis devices
- **Authentication**: LDAP integration
- **Build System**: Rollup

### Key Complex Components
1. **Nomenclature System**: Complex naming logic with ordinatore-driven rules
2. **Scan Handling**: Image processing, preview generation, sequencing
3. **Scanner Integration**: Real-time TCP communication with Metis devices
4. **Image Viewer**: OpenSeadragon-based viewer with crop areas and overlays

## Migration Strategy

### 1. Gradual Migration Approach

**Phase 1: Backend Migration (Tauri Core)**
- Migrate IPC handlers to <PERSON><PERSON> commands
- Port database operations to Tauri
- Implement scanner communication in Rust

**Phase 2: Frontend Modernization**
- Upgrade Svelte to latest version (4 or 5)
- Modernize component architecture
- Maintain existing UI patterns

**Phase 3: Advanced Features**
- Optimize image processing with Rust
- Enhance scanner communication
- Add new capabilities

### 2. Architecture Mapping

| Current (Electron) | Tauri Equivalent | Implementation Strategy |
|-------------------|------------------|------------------------|
| IPC Handlers | Tauri Commands | Direct port with Rust functions |
| Main Process | Tauri Core (Rust) | Rewrite core logic in Rust |
| Preload Script | Tauri API | Use `@tauri-apps/api` |
| Node.js Dependencies | Rust Crates | Replace with Rust equivalents |
| Svelte Frontend | Svelte Frontend | Keep with minimal changes |

## Programming Patterns for Tauri

### 1. Command-Based Architecture

```rust
// Tauri commands replace your IPC handlers
#[tauri::command]
async fn db_get_scansioni(id_digit: i32) -> Result<Vec<Scansione>, String> {
    // Database logic here
}

#[tauri::command] 
async fn metis_start_scan(params: ScanParams) -> Result<String, String> {
    // Scanner communication logic
}
```

### 2. State Management

```rust
// Use Tauri's state management for shared data
#[derive(Default)]
struct AppState {
    scanner_connection: Mutex<Option<TcpStream>>,
    current_job: Mutex<Option<Job>>,
}
```

### 3. Event System

```rust
// Replace D3 events with Tauri events
app.emit_all("scan-completed", ScanResult { id: scan_id })?;
```

## Complex Component Migration

### Nomenclature Module Migration

**Current Implementation** (buildDisplayName function):
- Complex naming logic with ordinatore rules
- Frontend-heavy business logic
- Database-driven configuration

**Migration Strategy:**
1. **Keep Frontend Logic**: The nomenclature UI logic can remain in Svelte
2. **Move Business Logic to Rust**: Create Rust structs for nomenclature rules
3. **Create Tauri Commands**: For database operations and validation

```rust
// Rust implementation
#[derive(Serialize, Deserialize)]
struct Nomenclatura {
    ordinatore: Ordinatore,
    interno_pagina: Option<i32>,
    numero_pagina: Option<i32>,
    tipo_pagina: Option<String>,
    lettera_pagina: Option<String>,
}

#[tauri::command]
fn build_display_name(nomenclatura: Nomenclatura) -> String {
    let mut display_name = String::new();
    
    // INTERNO
    if let Some(interno) = nomenclatura.interno_pagina {
        if interno > 0 {
            display_name.push_str(&format!("-sub.{:04}_", interno));
        }
    }
    
    // ORDINATORE
    display_name.push_str(&nomenclatura.ordinatore.name_short);
    
    // Additional logic for page numbers, letters, etc.
    // Port the existing JavaScript logic to Rust
    
    display_name
}
```

### Scan Handling Migration

**Current Implementation** (ScanCommandManager):
- Complex scan workflow management
- Progress tracking and UI updates
- Database operations and file handling

**Migration Strategy:**
1. **Rust Scanner Communication**: Use `tokio` for async TCP communication
2. **Background Tasks**: Use Tauri's async commands for long-running operations
3. **Progress Updates**: Use Tauri events for real-time progress

```rust
#[tauri::command]
async fn start_scan(
    app_handle: tauri::AppHandle,
    params: ScanParams
) -> Result<i32, String> {
    // Validate scan parameters
    if !preliminary_scan_check(&params) {
        return Err("Scan validation failed".to_string());
    }
    
    // Start background scanning task
    let app_handle_clone = app_handle.clone();
    tokio::spawn(async move {
        // Emit progress updates
        app_handle_clone.emit_all("scan-progress", ScanProgress {
            action: "scan".to_string(),
            status: "Inizializzazione".to_string(),
            progress: 0,
        }).unwrap();
        
        // Perform actual scanning logic
        // Database operations, file handling, etc.
    });
    
    Ok(scan_id)
}
```

### Leggio Mode Implementation

**Current Implementation**:
- Complex sequencing logic for recto/verso scans
- Mode-specific UI behavior
- Sequence gap detection for verso scans

**Migration Strategy**:
```rust
#[derive(Serialize, Deserialize)]
enum AcquisitionMode {
    Normal,
    LeggioRecto,
    LeggioVerso,
}

#[tauri::command]
fn calculate_next_sequenziale(
    scans: Vec<Scan>,
    mode: AcquisitionMode
) -> i32 {
    match mode {
        AcquisitionMode::Normal => {
            scans.iter().map(|s| s.sequenziale_scansione).max().unwrap_or(0) + 1
        },
        AcquisitionMode::LeggioRecto => {
            scans.iter().map(|s| s.sequenziale_scansione).max().unwrap_or(0) + 2
        },
        AcquisitionMode::LeggioVerso => {
            // Find first available gap for verso scan
            let mut sorted_scans = scans;
            sorted_scans.sort_by_key(|s| s.sequenziale_scansione);
            
            for i in 0..sorted_scans.len() - 1 {
                let current = sorted_scans[i].sequenziale_scansione;
                let next = sorted_scans[i + 1].sequenziale_scansione;
                
                if next - current >= 2 {
                    return current + 1;
                }
            }
            
            // No gap found, use next available
            sorted_scans.last().map(|s| s.sequenziale_scansione + 1).unwrap_or(1)
        }
    }
}
```

## Technology Stack Recommendations

### Rust Dependencies (Cargo.toml)

```toml
[dependencies]
tauri = { version = "1.0", features = ["api-all"] }
tokio = { version = "1.0", features = ["full"] }
sqlx = { version = "0.7", features = ["mysql", "runtime-tokio-rustls"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
image = "0.24"  # For image processing (replaces Sharp)
ldap3 = "0.11"  # For LDAP authentication
uuid = "1.0"
chrono = { version = "0.4", features = ["serde"] }
anyhow = "1.0"  # For error handling
```

### Frontend Dependencies (package.json)

```json
{
  "@tauri-apps/api": "^1.5.0",
  "svelte": "^4.0.0",
  "@smui/button": "^7.0.0",
  "@smui/select": "^7.0.0",
  "@smui/data-table": "^7.0.0",
  "openseadragon": "^4.1.0",
  "d3-selection": "^3.0.0",
  "svelte-spa-router": "^3.2.0"
}
```

## Migration Benefits

1. **Performance**: Rust backend significantly faster than Node.js
2. **Memory Usage**: Lower memory footprint compared to Electron
3. **Security**: Tauri's security model more robust than Electron
4. **Bundle Size**: Smaller application bundles
5. **Cross-platform**: Better native integration
6. **Type Safety**: Rust's type system prevents many runtime errors

## Migration Challenges & Solutions

### Challenge 1: Node.js Dependencies
**Problem**: Heavy reliance on Node.js ecosystem (mysql2, sharp, fs-extra)
**Solution**: Replace with Rust crates:
- mysql2 → sqlx
- sharp → image crate
- fs-extra → std::fs + tokio::fs

### Challenge 2: Complex Scanner Communication
**Problem**: Intricate TCP protocol with Metis devices
**Solution**: Port TCP logic to Rust with tokio for better async handling and error management

### Challenge 3: Image Processing Pipeline
**Problem**: Sharp-based image processing and DZI generation
**Solution**: Use Rust image processing crates for better performance and memory management

### Challenge 4: Frontend State Management
**Problem**: Complex D3-based event system and Svelte stores
**Solution**: Migrate to Tauri events and modern Svelte state management

## Recommended Migration Timeline

### Week 1-2: Project Setup
- [ ] Initialize Tauri project structure
- [ ] Set up Rust workspace and basic commands
- [ ] Configure build system and development environment

### Week 3-4: Core Backend Migration
- [ ] Migrate database operations (sqlx integration)
- [ ] Port authentication system (LDAP)
- [ ] Implement basic Tauri commands

### Week 5-6: Scanner Communication
- [ ] Port Metis TCP communication to Rust
- [ ] Implement async scanner operations
- [ ] Add progress tracking and error handling

### Week 7-8: Image Processing & File Operations
- [ ] Migrate Sharp functionality to Rust image crates
- [ ] Implement DZI generation
- [ ] Port file system operations

### Week 9-10: Frontend Integration
- [ ] Update Svelte components to use Tauri APIs
- [ ] Migrate from IPC to Tauri commands
- [ ] Update event system

### Week 11-12: Testing & Optimization
- [ ] Comprehensive testing of all features
- [ ] Performance optimization
- [ ] Bug fixes and refinements

## Next Steps

1. **Start with a Proof of Concept**: Create a minimal Tauri app with one core feature
2. **Database Migration First**: Begin with database operations as they're foundational
3. **Incremental Testing**: Test each migrated component thoroughly
4. **Maintain Parallel Development**: Keep current Electron version running during migration

## Additional Resources

- [Tauri Documentation](https://tauri.app/v1/guides/)
- [Tauri Migration Guide](https://tauri.app/v1/guides/migration/from-electron)
- [Rust Book](https://doc.rust-lang.org/book/)
- [Tokio Documentation](https://tokio.rs/)
- [SQLx Documentation](https://docs.rs/sqlx/)
